import { CasinoFreespinBonus } from '@/entities/pronet/CasinoFreespinBonus';
import { pgCasinoTraderAdminHttpClient } from '@/network/pg-ct/PGCasinoTraderApiClient';
import { FreespinBonusPreloadRequest } from '@/network/pg-ct/requests/freespinBonus/FreespinBonusPreloadRequest';
import * as FreespinBonusCreate from '@/network/pg-ct/requests/freespinBonus/create';

export class PGCasinoTraderService {
  static async assignFreeSpinBonus(freespinBonus: CasinoFreespinBonus, customerCode: string) {
    try {
      const preloadRequest = new FreespinBonusPreloadRequest();
      const preloadResult = await pgCasinoTraderAdminHttpClient.makeRequest(preloadRequest);
      if (!preloadResult.success) {
        throw new Error(preloadResult.message);
      }

      const enterCreateModeRequest = new FreespinBonusCreate.FreespinBonusEnterCreateModeRequest({
        javax: preloadResult.data,
      });
      const enterCreateModeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        enterCreateModeRequest,
        preloadResult.viewState || '',
      );
      if (!enterCreateModeResult.success) {
        throw new Error(enterCreateModeResult.message);
      }

      const changeVendorCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        field: 'vendor',
        value: freespinBonus.vendorId.toString(),
      });
      const changeVendorCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        changeVendorCreateFormRequest,
        preloadResult.viewState || '',
      );
      if (!changeVendorCreateFormResult.success) {
        throw new Error(changeVendorCreateFormResult.message);
      }

      const selectGameRequests = freespinBonus.gameIds.map(
        (gameId: number) =>
          new FreespinBonusCreate.FreespinBonusSelectGameRequest({
            javax: {
              ...preloadResult.data,
              ...enterCreateModeResult.data,
            },
            gameId,
            gameIds: freespinBonus.gameIds,
          }),
      );
      for (const selectGameRequest of selectGameRequests) {
        const selectGameResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          selectGameRequest,
          preloadResult.viewState || '',
        );
        if (!selectGameResult.success) {
          throw new Error(selectGameResult.message);
        }
      }

      const tabChangeRequest = new FreespinBonusCreate.FreespinBonusTabChangeRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        tab: 'customers',
      });
      const tabChangeResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        tabChangeRequest,
        preloadResult.viewState || '',
      );
      if (!tabChangeResult.success) {
        throw new Error(tabChangeResult.message);
      }

      const customerSearchRequest = new FreespinBonusCreate.FreespinBonusCustomerSearchRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
          ...tabChangeResult.data,
        },
        customerCode: customerCode,
      });
      const customerSearchResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        customerSearchRequest,
        preloadResult.viewState || '',
      );
      if (!customerSearchResult.success) {
        throw new Error(customerSearchResult.message);
      }

      const customerId = customerSearchResult.data[0]?.customerId;
      if (!customerId) {
        throw new Error('Customer not found');
      }

      const selectCustomerRequest = new FreespinBonusCreate.FreespinBonusSelectCustomerRequest({
        javax: {
          ...preloadResult.data,
          ...enterCreateModeResult.data,
        },
        customerId: customerId,
      });
      const selectCustomerResult = await pgCasinoTraderAdminHttpClient.makeRequest(
        selectCustomerRequest,
        preloadResult.viewState || '',
      );
      if (!selectCustomerResult.success) {
        throw new Error(selectCustomerResult.message);
      }

      if (freespinBonus.values['currencyCode']) {
        const changeCurrencyCreateFormRequest = new FreespinBonusCreate.FreespinBonusChangeCreateFormRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          field: 'cmbCurrency',
          value: freespinBonus.values['currencyCode'] as string,
        });
        const changeCurrencyCreateFormResult = await pgCasinoTraderAdminHttpClient.makeRequest(
          changeCurrencyCreateFormRequest,
          preloadResult.viewState || '',
        );
      }

      const result = await pgCasinoTraderAdminHttpClient.makeRequest(
        new FreespinBonusCreate.FreespinBonusCreateRequest({
          javax: {
            ...preloadResult.data,
            ...enterCreateModeResult.data,
          },
          providerId: freespinBonus.vendorId,
          customerId: customerId,

          expiresAt: freespinBonus.bonus.expiresAt ?? new Date(),
          gameIds: freespinBonus.gameIds,

          currencyId: freespinBonus.values['currencyId'] as number,
          betAmount: freespinBonus.values['betAmount'] as number,
          betPerLine: freespinBonus.values['betPerLine'] as number,
          maxWin: freespinBonus.values['maxWin'] as number,
          nOfFreespins: freespinBonus.values['nOfFreespins'] as number,
          minWinAmountRequired: freespinBonus.values['minWinAmountRequired'] as number,
          freeRoundBalance: freespinBonus.values['freeRoundBalance'] as number,
          lines: freespinBonus.values['lines'] as number,
          coins: freespinBonus.values['coins'] as number,
          denomination: freespinBonus.values['denomination'] as number,
        }),
        preloadResult.viewState || '',
      );

      if (!result.success) {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('❌ Failed to create freespin bonus:', error);

      throw error;
    }
  }
}
