import { AppDataSource } from '@/database/data-source';
import { Bonus<PERSON><PERSON><PERSON><PERSON><PERSON>, BonusRuleCheckResult } from '../bonusRuleChecker.service';
import { PgDagurService } from '@/services/thirdparty/pg-dagur';
import { Bonus } from '@/entities/pronet/Bonus';
import { CasinoLossbackBonus } from '@/entities/pronet/CasinoLossbackBonus';

export class <PERSON>back<PERSON>hecker implements BonusRuleChecker {
  async checkEligibility(userId: number, bonus: Bonus): Promise<BonusRuleCheckResult> {
    console.log(`Checking lossback eligibility for user ${userId}, bonus ${bonus.id}`);

    const bonusType = bonus.type;

    if (bonusType !== 'lossback') {
      throw new Error('Bonus type mismatch');
    }

    // Retrieve lossback bonus details
    const lossbackBonusRepository = AppDataSource.getRepository(CasinoLossbackBonus);
    const lossbackBonus = await lossbackBonusRepository.findOne({
      where: {
        bonusId: bonus.id
      }
    });

    if (!lossbackBonus) {
      throw new Error('Lossback bonus not found');
    }

    // Initialize eligibility tracking variables
    let isWithinHappyHours = false;

    // Retrieve user statistics from PG Dagur
    let userStatistics: any = null;
    try {
      console.log(`Fetching user statistics for customer ${userId}...`);
      userStatistics = await PgDagurService.getCustomerStatistics(userId.toString());
      console.log(`✅ Successfully retrieved user statistics for customer ${userId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve user statistics for customer ${userId}:`, error);
      // Continue without statistics - don't fail the entire eligibility check
    }

    // Retrieve customer summary data from PG Dagur
    let customerSummary: any = null;
    try {
      console.log(`Fetching customer summary for customer ${userId}...`);
      customerSummary = await PgDagurService.getCustomerSummary(userId.toString());
      console.log(`✅ Successfully retrieved customer summary for customer ${userId}`);
      console.log(`👤 Customer info:`, customerSummary.customer);
      console.log(`📋 Customer details:`, customerSummary.customerDetail);
      console.log(`💰 Customer balances:`, customerSummary.balances);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve customer summary for customer ${userId}:`, error);
      // Continue without summary - don't fail the entire eligibility check
    }

    // Retrieve customer details from PG Dagur
    let customerDetails: any = null;
    try {
      console.log(`Fetching customer details for customer ${userId}...`);
      customerDetails = await PgDagurService.getCustomerDetails(userId.toString());
      console.log(`✅ Successfully retrieved customer details for customer ${userId}`);
    } catch (error) {
      console.warn(`⚠️ Failed to retrieve customer details for customer ${userId}:`, error);
      // Continue without details - don't fail the entire eligibility check
    }

    const username = customerSummary?.customer?.username;

    // If customer balance exceeds threshold, they are not eligible - return immediately
    if (customerSummary?.calculatedData?.sportsFullBalance > lossbackBonus.maxBalance) {
      return {
        isEligible: false,
        reason: `Customer has more than ${lossbackBonus.maxBalance} TRY in their sports full balance`,
        metadata: {
          bonusType: 'lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          username: username || null,
          customerSummary: customerSummary || null,
          failureReason: 'balance_exceeded'
        }
      };
    }

    // Check user transactions using PG Dagur Service
    let userTransactions: any = null;
    try {
      console.log(`Fetching user transactions for username: ${username}...`);
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 1); // Last 24 hours

      userTransactions = await PgDagurService.listTransactions({
        startDate,
        endDate,
        status: ['C'], // Completed transactions
        username: username,
        page: 1,
        limit: 100,
        loadSubtotals: false, // Don't need subtotals for bonus rule checking
      });

    } catch (error) {
      console.warn(`⚠️ Failed to retrieve user transactions for ${username}:`, error);
      // Continue without transactions - don't fail the entire eligibility check
    }

    // If user has transactions of type "P" in the last 24 hours, they are not eligible - return immediately
    if (userTransactions?.transactions?.items?.some((t: any) => t.status === 'P')) {
      return {
        isEligible: false,
        reason: 'Customer has a pending transaction',
        metadata: {
          bonusType: 'lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          username: username || null,
          userTransactions: userTransactions?.transactions?.items || null,
          transactionCount: userTransactions?.transactions?.items?.length || 0,
          customerSummary: customerSummary || null,
          failureReason: 'pending_transaction'
        }
      };
    }

    // Calculate the difference between deposits and withdrawals
    const depositAmount = userTransactions?.transactions?.items
      .filter((t: any) => t.masterCode === 'D')
      .reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
    const withdrawalAmount = userTransactions?.transactions?.items
      .filter((t: any) => t.masterCode === 'W')
      .reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
    
    const depositWithdrawDiff = depositAmount - withdrawalAmount;

    // If the difference is less than 100 TRY, they are not eligible - return immediately
    if (depositWithdrawDiff < 100) {
      return {
        isEligible: false,
        reason: 'Customer has less than 100 TRY difference between deposits and withdrawals',
        metadata: {
          bonusType: 'lossback',
          userId,
          bonusId: bonus.id,
          checkedAt: new Date().toISOString(),
          isWithinHappyHours: false,
          username: username || null,
          depositWithdrawDiff: depositWithdrawDiff,
          userTransactions: userTransactions?.transactions?.items || null,
          transactionCount: userTransactions?.transactions?.items?.length || 0,
          customerSummary: customerSummary || null,
          failureReason: 'insufficient_deposit_withdraw_diff'
        }
      };
    }

    const latestDeposit = userTransactions?.transactions?.items
      .filter((t: any) => t.masterCode === 'D')
      .reduce((latest: any, t: any) => {
        if (!latest || t.transactionDate > latest.transactionDate) {
          return t;
        }
        return latest;
      }, null);

    // Check if the request was made during happy hours
    const currentTime = new Date();
    if (this.requestedDuringHappyHours(lossbackBonus, currentTime)) {
      isWithinHappyHours = true;
    }


    // All checks passed - user is eligible
    return {
      isEligible: true,
      metadata: {
        bonusType: 'lossback',
        userId,
        bonusId: bonus.id,
        checkedAt: new Date().toISOString(),
        isWithinHappyHours: isWithinHappyHours,
        username: username || null,
        depositWithdrawDiff: depositWithdrawDiff,
        latestDeposit: latestDeposit || null,
        userTransactions: userTransactions?.transactions?.items || null,
        transactionCount: userTransactions?.transactions?.items?.length || 0,
        userStatistics: userStatistics || null,
        customerSummary: customerSummary || null,
        customerDetails: customerDetails || null,
      }
    };
  }

  private requestedDuringHappyHours(bonus: CasinoLossbackBonus, currentTime: Date): boolean {
    const happyHoursStart = bonus.happyHoursStart;
    const happyHoursEnd = bonus.happyHoursEnd;

    // If happy hours are not defined, return false
    if (!happyHoursStart || !happyHoursEnd) {
      return false;
    }

    // Convert time strings to Date objects for comparison
    // Time strings are in format "HH:MM:SS" (e.g., "09:30:00", "18:00:00")
    const today = new Date(currentTime);
    today.setHours(0, 0, 0, 0); // Reset to start of day

    const startTime = new Date(today);
    const [startHour, startMinute, startSecond] = happyHoursStart.split(':').map(Number);
    startTime.setHours(startHour || 0, startMinute || 0, startSecond || 0);

    const endTime = new Date(today);
    const [endHour, endMinute, endSecond] = happyHoursEnd.split(':').map(Number);
    endTime.setHours(endHour || 0, endMinute || 0, endSecond || 0);

    // Handle case where happy hours span midnight (e.g., 22:00 to 06:00)
    if (endTime <= startTime) {
      // Happy hours span midnight
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      // Normal case: happy hours within the same day
      return currentTime >= startTime && currentTime <= endTime;
    }
  }
}