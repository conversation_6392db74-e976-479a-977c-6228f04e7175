import { AppDataSource } from '@/database/data-source';
import { Bonus } from '@/entities/pronet/Bonus';
import { BonusClaim } from '@/entities/pronet/BonusClaim';
import { BonusTemplate } from '@/entities/pronet/BonusTemplate';
import { Customer } from '@/entities/pronet/Customer';
import { EntityManager, FindOptionsWhere, In, IsNull } from 'typeorm';
import { paginate, Paginated } from '../shared';
import { BonusPromocode } from '@/entities/pronet/BonusPromocode';
import { BonusPromocodeActivation } from '@/entities/pronet/BonusPromocodeActivation';
import { BonusBulkAssignmentJob } from '@/entities/pronet/BonusBulkAssignmentJob';
import { PGCasinoTraderService } from '../thirdparty/pg-ct';
import { CasinoFreespinBonus } from '@/entities/pronet/CasinoFreespinBonus';
import { PgDagurService } from '../thirdparty/pg-dagur';
import { SiteClaimableBonusService } from './siteClaimableBonus.service';
import { BonusRuleCheckerService, BonusRuleCheckResult } from './bonusRuleChecker/bonusRuleChecker.service';
import { CasinoLossbackBonus } from '@/entities/pronet/CasinoLossbackBonus';
import { BonusBulkAssignmentJobTarget } from '@/entities/pronet/BonusBulkAssignmentJobTarget';
import { BONUS_BULK_ASSIGNMENT_JOB_TARGET_TIMEOUT_MS } from '../constants';

export interface BonusClaimsSearch {
  externalCustomerId?: number;
  customerId?: number;
  username?: string;
  bonusId?: number;
  bonusType?: string;

  page: number;
  limit: number;
}

export interface BonusPromocodeCreate {
  code: string;
  bonusId: number;
  maxActivations: number;
}

export interface SearchBonusPromocode {
  code?: string;
  bonusId?: number;
  bonusType?: string;
  isActive?: boolean;

  page: number;
  limit: number;
}

export interface SearchPromocodeActivation {
  promocodeId?: number;
  customerId?: number;

  page: number;
  limit: number;
}

export interface BonusClaimCreate {
  bonusId: number;
  customerId: number;
  source: 'manual' | 'promocode' | 'bulk-assignment' | 'button';
}

export interface SearchBonus {
  type?: string;
  isActive?: boolean;

  page: number;
  limit: number;
}

export interface SearchBonusBulkAssignmentJob {
  bonusId?: number;
  bonusType?: string;
  status?: BonusBulkAssignmentJob['status'];

  page: number;
  limit: number;
}

export interface BonusUpdate {
  description?: string;
  reward?: string;
  isActive?: boolean;
}

export class BonusService {
  async update(id: number, data: BonusUpdate): Promise<Bonus> {
    const repository = AppDataSource.getRepository(Bonus);

    // Build update object with only provided fields
    const updateData: Partial<Bonus> = {};
    if (data.description !== undefined) {
      updateData.description = data.description;
    }
    if (data.reward !== undefined) {
      updateData.reward = data.reward;
    }
    if (data.isActive !== undefined) {
      updateData.isActive = data.isActive;
    }

    // If no fields to update, throw error
    if (Object.keys(updateData).length === 0) {
      throw new Error('No fields provided to update');
    }

    const result = await repository.update(id, updateData);

    if (result.affected !== 1) {
      throw new Error('Failed to update bonus');
    }

    const bonus = await repository.findOne({ where: { id }, relations: ['rules'] });
    if (!bonus) {
      throw new Error('Bonus not found');
    }

    return bonus;
  }

  async toggleActive(id: number, value: boolean): Promise<Bonus> {
    const repository = AppDataSource.getRepository(Bonus);
    const result = await repository.update(id, { isActive: value });

    if (result.affected !== 1) {
      throw new Error('Failed to update bonus');
    }

    const bonus = await repository.findOne({ where: { id }, relations: ['rules'] });
    if (!bonus) {
      throw new Error('Failed to update bonus');
    }

    return bonus;
  }

  async softDelete(id: number): Promise<Bonus> {
    const repository = AppDataSource.getRepository(Bonus);
    const result = await repository.update(id, { isActive: false, deletedAt: new Date() });

    if (result.affected !== 1) {
      throw new Error('Failed to delete bonus');
    }

    const bonus = await repository.findOne({ where: { id }, relations: ['rules'] });
    if (!bonus) {
      throw new Error('Failed to delete bonus');
    }

    return bonus;
  }

  async softDeleteTemplate(id: number): Promise<BonusTemplate> {
    const repository = AppDataSource.getRepository(BonusTemplate);
    const result = await repository.update(id, { deletedAt: new Date() });

    if (result.affected !== 1) {
      throw new Error('Failed to delete bonus template');
    }

    const template = await repository.findOne({ where: { id } });
    if (!template) {
      throw new Error('Failed to delete bonus template');
    }

    return template;
  }

  async searchClaims(data: BonusClaimsSearch): Promise<Paginated<BonusClaim>> {
    const customerFilter: FindOptionsWhere<Customer> = {};
    const bonusFilter: FindOptionsWhere<Bonus> = {};

    if (data.externalCustomerId) {
      customerFilter.externalId = data.externalCustomerId;
    }

    if (data.customerId) {
      customerFilter.id = data.customerId;
    }

    if (data.username) {
      customerFilter.username = data.username;
    }

    if (data.bonusType) {
      bonusFilter.type = data.bonusType;
    }

    if (data.bonusId) {
      bonusFilter.id = data.bonusId;
    }

    const [items, total] = await AppDataSource.getRepository(BonusClaim).findAndCount({
      relations: ['customer', 'bonus'],
      where: {
        customer: customerFilter,
        bonus: bonusFilter,
      },
      order: {
        createdAt: 'DESC',
      },
      skip: (data.page - 1) * data.limit,
      take: data.limit,
    });

    return paginate(items, total, data.page, data.limit);
  }

  async createPromocode(data: BonusPromocodeCreate): Promise<BonusPromocode> {
    const promocode = AppDataSource.getRepository(BonusPromocode).create({
      code: data.code,
      bonusId: data.bonusId,
      activations: 0,
      maxActivations: data.maxActivations,
      isActive: true,
    });

    await AppDataSource.getRepository(BonusPromocode).save(promocode);

    const result = await AppDataSource.getRepository(BonusPromocode).findOne({
      where: { id: promocode.id },
      relations: ['bonus', 'bonus.rules'],
    });
    if (!result) {
      throw new Error('Failed to create promocode');
    }

    return result;
  }

  async activatePromocode(code: string, customerId: number): Promise<BonusPromocodeActivation> {
    const id = await AppDataSource.transaction(async (manager) => {
      const promocodeRepository = manager.getRepository(BonusPromocode);
      const promocode = await promocodeRepository.findOne({
        where: { code },
      });

      if (!promocode) {
        throw new Error('Promocode not found');
      }

      if (!promocode.isActive) {
        throw new Error('Promocode is not active');
      }

      const activationRepository = manager.getRepository(BonusPromocodeActivation);
      const activation = activationRepository.create({
        promocodeId: promocode.id,
        customerId,
      });

      await activationRepository.save(activation);

      await promocodeRepository.increment({ id: promocode.id }, 'activations', 1);

      await this.claimTx(
        {
          bonusId: promocode.bonusId,
          customerId,
          source: 'promocode',
        },
        true,
        manager,
      );

      return activation.id;
    });

    const activation = await AppDataSource.getRepository(BonusPromocodeActivation).findOne({
      where: { id: id },
      relations: ['promocode', 'customer'],
    });
    if (!activation) {
      throw new Error('Failed to activate promocode');
    }

    return activation;
  }

  async searchPromocodes(data: SearchBonusPromocode): Promise<Paginated<BonusPromocode>> {
    const promocodeFilter: FindOptionsWhere<BonusPromocode> = {};

    if (data.code) {
      promocodeFilter.code = data.code;
    }

    if (data.bonusId) {
      promocodeFilter.bonusId = data.bonusId;
    }

    if (data.bonusType) {
      promocodeFilter.bonus = { type: data.bonusType };
    }

    if (data.isActive !== undefined) {
      promocodeFilter.isActive = data.isActive;
    }

    const [items, total] = await AppDataSource.getRepository(BonusPromocode).findAndCount({
      where: promocodeFilter,
      relations: ['bonus', 'bonus.rules'],
      skip: (data.page - 1) * data.limit,
      take: data.limit,
    });

    return paginate(items, total, data.page, data.limit);
  }

  async searchPromocodeActivations(data: SearchPromocodeActivation): Promise<Paginated<BonusPromocodeActivation>> {
    const activationFilter: FindOptionsWhere<BonusPromocodeActivation> = {};

    if (data.promocodeId) {
      activationFilter.promocodeId = data.promocodeId;
    }

    if (data.customerId) {
      activationFilter.customerId = data.customerId;
    }

    const [items, total] = await AppDataSource.getRepository(BonusPromocodeActivation).findAndCount({
      where: activationFilter,
      relations: ['promocode', 'customer'],
      skip: (data.page - 1) * data.limit,
      take: data.limit,
    });

    return paginate(items, total, data.page, data.limit);
  }

  async searchBonuses(data: SearchBonus): Promise<Paginated<Bonus>> {
    const bonusFilter: FindOptionsWhere<Bonus> = {
      deletedAt: IsNull(),
    };

    if (data.isActive !== undefined) {
      bonusFilter.isActive = data.isActive;
    }

    if (data.type) {
      bonusFilter.type = data.type;
    }

    const [items, total] = await AppDataSource.getRepository(Bonus).findAndCount({
      where: bonusFilter,
      relations: ['rules'],
      skip: (data.page - 1) * data.limit,
      take: data.limit,
    });

    return paginate(items, total, data.page, data.limit);
  }

  async togglePromocode(id: number, value: boolean): Promise<BonusPromocode> {
    const repository = AppDataSource.getRepository(BonusPromocode);
    const result = await repository.update(id, { isActive: value });

    if (result.affected !== 1) {
      throw new Error('Failed to update promocode');
    }

    const promocode = await repository.findOne({ where: { id }, relations: ['bonus', 'bonus.rules'] });
    if (!promocode) {
      throw new Error('Failed to update promocode');
    }

    return promocode;
  }

  async softDeletePromocode(id: number): Promise<BonusPromocode> {
    const repository = AppDataSource.getRepository(BonusPromocode);
    const result = await repository.update(id, { isActive: false, deletedAt: new Date() });

    if (result.affected !== 1) {
      throw new Error('Failed to delete promocode');
    }

    const promocode = await repository.findOne({ where: { id }, relations: ['bonus', 'bonus.rules'] });
    if (!promocode) {
      throw new Error('Failed to delete promocode');
    }

    return promocode;
  }

  // @todo async check if user matches rules
  private async claimTx(
    data: BonusClaimCreate,
    checkRules: boolean,
    tx: EntityManager,
    shouldCancel?: () => Promise<boolean>,
  ): Promise<BonusClaim> {
    const bonusRepository = tx.getRepository(Bonus);
    const bonus = await bonusRepository.findOne({ where: { id: data.bonusId } });

    if (!bonus) {
      throw new Error('Bonus not found');
    }

    if (!bonus.isActive) {
      throw new Error('Bonus is not active');
    }

    if (bonus.expiresAt && bonus.expiresAt < new Date()) {
      throw new Error('Bonus has expired');
    }

    // Validate that the bonus is claimable via the rewards page
    const siteClaimableBonusService = new SiteClaimableBonusService();
    await siteClaimableBonusService.validateBonusClaimable(data.bonusId);

    const customerRepository = tx.getRepository(Customer);
    const customer = await customerRepository.findOne({ where: { id: data.customerId } });

    if (!customer) {
      throw new Error('Customer not found');
    }

    let checkResult: BonusRuleCheckResult | null = null;

    if (checkRules) {
      const checker = new BonusRuleCheckerService();
      checkResult = await checker.checkBonusEligibility(customer.externalId, data.bonusId);

      if (!checkResult.isEligible) {
        throw new Error(checkResult.reason);
      }
    }

    const bonusClaimRepository = tx.getRepository(BonusClaim);
    const claim = bonusClaimRepository.create({
      bonusId: data.bonusId,
      customerId: data.customerId,
      source: data.source,
    });

    if (await shouldCancel?.()) {
      throw new Error('Operation cancelled');
    }

    switch (bonus.type) {
      case 'freespin':
        {
          const freespinBonus = await tx.getRepository(CasinoFreespinBonus).findOne({
            where: { bonusId: data.bonusId },
            relations: ['bonus'],
          });
          if (!freespinBonus) {
            throw new Error('Freespin bonus not found');
          }

          await PgDagurService.createBonusNote(customer.externalId, `${bonus.type} [${bonus.id}]: attempt`);
          await PGCasinoTraderService.assignFreeSpinBonus(freespinBonus, customer.code);
          try {
            await PgDagurService.createBonusNote(
              customer.externalId,
              `${bonus.type} [${bonus.id}]: assigned - ${bonus.name}`,
            );
          } catch (e) {
            console.error('❌ Failed to create bonus note:', e);
          }
        }
        break;
      case 'lossback':
        {
          // Retrieve lossback bonus details
          const lossbackBonusRepository = tx.getRepository(CasinoLossbackBonus);
          const lossbackBonus = await lossbackBonusRepository.findOne({
            where: { bonusId: data.bonusId },
          });

          if (!lossbackBonus) {
            throw new Error('Lossback bonus not found');
          }

          let lossbackPercentage = lossbackBonus.lossbackPercentage;
          const latestDepositAmount = checkResult?.metadata?.['latestDeposit'].amount;

          if (checkResult && checkResult.metadata && checkResult.metadata['isWithinHappyHours']) {
            lossbackPercentage *= 2;
          }

          await PgDagurService.grantCash(
            customer.externalId,
            `${bonus.type} [${bonus.id}]: assigned - ${bonus.name}`,
            latestDepositAmount * (lossbackPercentage / 100),
          );
        }
        break;
      default:
        throw new Error('Bonus type not supported');
    }

    return await bonusClaimRepository.save(claim);
  }

  async claim(data: BonusClaimCreate, checkRules = true): Promise<BonusClaim> {
    return await AppDataSource.transaction((manager) => this.claimTx(data, checkRules, manager));
  }

  async claimForBulkAssignmentJobTarget(targetId: number, customerId: number): Promise<BonusClaim> {
    const target = await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).findOne({
      where: { id: targetId },
      relations: ['bonusBulkAssignmentJob', 'customer'],
    });
    if (!target) {
      throw new Error('Target not found');
    }

    if (target.status === 'processed') {
      throw new Error('Target already processed');
    }

    if (target.bonusBulkAssignmentJob.status === 'pending') {
      target.bonusBulkAssignmentJob.status = 'processing';
      await AppDataSource.getRepository(BonusBulkAssignmentJob).save(target.bonusBulkAssignmentJob);
    } else if (target.bonusBulkAssignmentJob.status === 'cancelled') {
      target.status = 'cancelled';
      await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).save(target);
      throw new Error('Job cancelled');
    } else if (target.bonusBulkAssignmentJob.status !== 'processing') {
      throw new Error('Job is not being processed');
    }

    if (
      target.status === 'processing' &&
      target.processedAt &&
      target.processedAt <= new Date(Date.now() - BONUS_BULK_ASSIGNMENT_JOB_TARGET_TIMEOUT_MS)
    ) {
      throw new Error('Target already being processed');
    }

    target.status = 'processing';
    target.events.push({
      event: 'info',
      message: 'Started processing',
      timestamp: new Date().toISOString(),
    });
    await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).save(target);

    try {
      const claim = await AppDataSource.transaction(async (manager) => {
        return this.claimTx(
          {
            bonusId: target.bonusBulkAssignmentJob.bonusId,
            customerId: customerId,
            source: 'bulk-assignment',
          },
          false,
          manager,
          async () => {
            const job = await this.getBulkAssignmentJob(target.bonusBulkAssignmentJob.id);

            return job.status === 'cancelled';
          },
        );
      });

      target.status = 'processed';
      target.events.push({
        event: 'info',
        message: 'Processing completed',
        timestamp: new Date().toISOString(),
      });
      await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).save(target);

      return claim;
    } catch (e) {
      try {
        target.status = 'failed';
        target.errorMessage = e instanceof Error ? e.message : 'Unknown error';
        target.processedAt = new Date();
        target.events.push({
          event: 'error',
          message: e instanceof Error ? e.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        });
        await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).save(target);
      } catch (e) {
        console.error('❌ Failed to update target status:', e);
      }

      throw e;
    }
  }

  async getBulkAssignmentJob(id: number): Promise<BonusBulkAssignmentJob> {
    const job = await AppDataSource.getRepository(BonusBulkAssignmentJob).findOne({
      where: { id },
      relations: ['targets', 'targets.customer', 'bonus', 'bonus.rules'],
    });
    if (!job) {
      throw new Error('Failed to fetch bulk assignment job');
    }

    if (job.status === 'cancelled') {
      // nothing
    } else {
      if (job.targets.every((t) => t.status === 'processed')) {
        job.status = 'processed';
      } else if (job.targets.some((t) => t.status === 'processing')) {
        job.status = 'processing';
      } else if (job.targets.some((t) => t.status === 'failed')) {
        job.status = 'failed';
      }
    }

    for (const target of job.targets) {
      const isTimeout =
        target.updatedAt.getTime() < new Date(Date.now() - BONUS_BULK_ASSIGNMENT_JOB_TARGET_TIMEOUT_MS).getTime();

      if (['processing', 'pending'].includes(target.status) && isTimeout) {
        target.status = 'timeout';
      }
    }

    if (job.targets.every((t) => t.status === 'timeout')) {
      job.status = 'timeout';
    }

    return job;
  }

  async getBonusBulkAssignmentJobTarget(id: number): Promise<BonusBulkAssignmentJobTarget | null> {
    return await AppDataSource.getRepository(BonusBulkAssignmentJobTarget).findOne({
      where: { id },
      relations: ['customer'],
    });
  }

  async listBulkAssignmentJobs(data: SearchBonusBulkAssignmentJob): Promise<Paginated<BonusBulkAssignmentJob>> {
    const jobFilter: FindOptionsWhere<BonusBulkAssignmentJob> = {};

    if (data.bonusId) {
      jobFilter.bonusId = data.bonusId;
    }

    if (data.bonusType) {
      jobFilter.bonus = { type: data.bonusType };
    }

    if (data.status) {
      jobFilter.status = data.status;
    }

    const [items, total] = await AppDataSource.getRepository(BonusBulkAssignmentJob).findAndCount({
      where: jobFilter,
      relations: ['targets', 'targets.customer', 'bonus', 'bonus.rules'],
      skip: (data.page - 1) * data.limit,
      take: data.limit,
    });

    for (const job of items) {
      if (job.status === 'cancelled') {
        // nothing
      } else {
        if (job.targets.every((t) => t.status === 'processed')) {
          job.status = 'processed';
        } else if (job.targets.some((t) => t.status === 'processing')) {
          job.status = 'processing';
        } else if (job.targets.some((t) => t.status === 'failed')) {
          job.status = 'failed';
        }
      }

      for (const target of job.targets) {
        const isTimeout =
          target.updatedAt.getTime() < new Date(Date.now() - BONUS_BULK_ASSIGNMENT_JOB_TARGET_TIMEOUT_MS).getTime();

        if (['processing', 'pending'].includes(target.status) && isTimeout) {
          target.status = 'timeout';
        }
      }

      if (job.targets.every((t) => t.status === 'timeout')) {
        job.status = 'timeout';
      }
    }

    return paginate(items, total, data.page, data.limit);
  }

  async cancelBulkAssignmentJob(id: number): Promise<BonusBulkAssignmentJob> {
    const repository = AppDataSource.getRepository(BonusBulkAssignmentJob);
    const result = await repository.update({ id, status: In(['pending', 'processing']) }, { status: 'cancelled' });

    if (result.affected !== 1) {
      throw new Error('Failed to cancel bulk assignment job');
    }

    const job = await repository.findOne({ where: { id }, relations: ['targets'] });
    if (!job) {
      throw new Error('Failed to cancel bulk assignment job');
    }

    return job;
  }

  async retryBulkAssignmentJob(id: number): Promise<BonusBulkAssignmentJob> {
    const job = await this.getBulkAssignmentJob(id);

    if (['cancelled', 'timeout', 'failed'].includes(job.status)) {
      job.status = 'pending';

      await AppDataSource.transaction(async (manager) => {
        await manager.getRepository(BonusBulkAssignmentJob).save(job);

        for (const target of job.targets) {
          if (['failed', 'timeout', 'cancelled'].includes(target.status) === false) {
            continue;
          }

          target.status = 'pending';
          target.errorMessage = null;
          target.processedAt = null;
          await manager.getRepository(BonusBulkAssignmentJobTarget).save(target);
        }
      });

      return job;
    } else {
      throw new Error('Job is not in a retryable state');
    }
  }
}
