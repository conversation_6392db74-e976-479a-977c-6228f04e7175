# Customer Statistics Pipeline

This directory contains the Dagur pipeline implementation for accessing customer statistics data.

## Overview

The customer statistics pipeline follows the standard Dagur 5-step sequence:
1. **Preload** - Uses the shared `CustomerDetailsPreloadRequest` to get initial page state and dynamic JSF element IDs
2. **Tab Change** - Switch to the statistics tab and retrieve basic statistics data
3. **Weekly Statistics** - Load additional weekly statistics data from the statistics tab
4. **Daily Statistics** - Load additional daily statistics data from the statistics tab
5. **Monthly Statistics** - Load additional monthly statistics data from the statistics tab

The controller automatically combines all responses into a single unified response.

The response is structured with nested categories to accommodate different statistics types:
- `gameStatistics`: Contains all gaming-related statistics from the main statistics tab
- `depositWithdrawStatistics`: Contains deposit/withdraw statistics from the main statistics tab
- `qrReferenceStatistics`: Contains QR/Reference deposit statistics from the main statistics tab
- `profitStatistics`: Contains profit statistics from the main statistics tab
- `casinoStatistics`: Contains casino statistics from the main statistics tab
- `manualDepositWithdrawStatistics`: Contains manual deposit/withdraw statistics from the main statistics tab
- `weeklyStatistics`: Contains weekly statistics data loaded via separate request
- `dailyStatistics`: Contains daily statistics data loaded via separate request
- `monthlyStatistics`: Contains monthly statistics data loaded via separate request
- Future categories can be added as needed

## API Endpoint

**GET** `/api/pg-dagur/v1/internal/customers/:customerId/statistics`

Returns combined customer statistics including game statistics, weekly statistics, daily statistics, and monthly statistics data.

## Files

### CustomerDetailsStatisticsTabChangeRequest.ts
- **Purpose**: Tab change request to switch to statistics tab and retrieve basic data
- **Method**: POST
- **Path**: `/restricted/customer-details.xhtml`
- **Form Data**: Uses the provided form data with dynamic JSF elements
- **Returns**: Customer statistics data and updated ViewState

### CustomerDetailsStatisticsWeeklyStatisticsRequest.ts
- **Purpose**: Load weekly statistics data from the statistics tab
- **Method**: POST
- **Path**: `/restricted/customer-details.xhtml`
- **Form Data**: Uses loadWeeklyStat action with dynamic JSF elements
- **Returns**: Customer weekly statistics data and updated ViewState

### CustomerDetailsStatisticsDailyStatisticsRequest.ts
- **Purpose**: Load daily statistics data from the statistics tab
- **Method**: POST
- **Path**: `/restricted/customer-details.xhtml`
- **Form Data**: Uses loadDailyStat action with dynamic JSF elements
- **Returns**: Customer daily statistics data and updated ViewState

### CustomerDetailsStatisticsMonthlyStatisticsRequest.ts
- **Purpose**: Load monthly statistics data from the statistics tab
- **Method**: POST
- **Path**: `/restricted/customer-details.xhtml`
- **Form Data**: Uses loadMonthlyStat action with dynamic JSF elements
- **Returns**: Customer monthly statistics data and updated ViewState

## Usage Example

### API Call
```bash
GET /api/pg-dagur/v1/internal/customers/12345/statistics
```

### Response
```json
{
  "success": true,
  "data": {
    "statistics": {
      "gameStatistics": {
        "lastBetDate": "2024-01-15",
        "incomeAffiliateCode": null,
        "trackboxAffiliateId": "TB456",
        "klasPokerUsername": null,
        "totalBonusWin": 150,
        "totalAssignedBonus": 500,
        "totalActivatedBonusAmount": 300,
        "totalFreebet": 9,
        "totalUsedFreebet": 1,
        "totalUnusedFreebet": 8,
        "totalJackpotJoinings": 5,
        "totalJackpotWinningCount": 2,
        "totalJackpotWinningAmount": 1000,
        "totalPayout": 2500,
        "totalDiscount": 3,
        "totalDiscountAmount": 75,
        "totalRakeback": 10,
        "totalRakebackAmount": 250,
        "totalDepositOfKlasPoker": 1000,
        "totalDepositNumberOfKlasPoker": 5,
        "totalWithdrawOfKlasPoker": 800,
        "totalWithdrawNumberOfKlasPoker": 3,
        "averageStake": 25,
        "lifetimeTurnover": 15000
      },
      "depositWithdrawStatistics": {
        "totalDepositAmount": 5000.00,
        "totalDepositNumber": 15,
        "totalWithdrawAmount": 3500.00,
        "totalWithdrawNumber": 8,
        "totalAtmOutageAmount": 50.00,
        "totalAtmOutageNumber": 2
      },
      "qrReferenceStatistics": {
        "totalQrReferenceDepositAmount": 1200.00,
        "totalQrReferenceDepositNumber": 3
      },
      "profitStatistics": {
        "totalProfitAmount": 1500.00,
        "profitPercentage": 15.5
      },
      "casinoStatistics": {
        "casinoTurnover": 25000.00,
        "casinoPayout": 22000.00,
        "casinoPL": 3000.00,
        "casinoPLPercentage": 12.0
      },
      "manualDepositWithdrawStatistics": {
        "manualTotalDepositAmount": 2000.00,
        "manualTotalDepositNumber": 5,
        "manualTotalWithdrawAmount": 1500.00,
        "manualTotalWithdrawNumber": 4
      },
      "weeklyStatistics": {
        "totalDepositAmount": 1500.50,
        "totalDepositNumber": 3,
        "totalWithdrawAmount": 800.00,
        "totalWithdrawNumber": 2,
        "totalBonusWin": 150.00,
        "totalAssignedBonus": 500.00,
        "totalActivatedBonusAmount": 300.00,
        "totalDepositOfKlasPoker": 1000.00,
        "totalDepositNumberOfKlasPoker": 5,
        "totalWithdrawOfKlasPoker": 800.00,
        "totalWithdrawNumberOfKlasPoker": 3
      },
      "dailyStatistics": {
        "totalDepositAmount": 500.00,
        "totalDepositNumber": 1,
        "totalWithdrawAmount": 200.00,
        "totalWithdrawNumber": 1,
        "totalBonusWin": 50.00,
        "totalAssignedBonus": 100.00,
        "totalActivatedBonusAmount": 75.00,
        "totalDepositOfKlasPoker": 300.00,
        "totalDepositNumberOfKlasPoker": 2,
        "totalWithdrawOfKlasPoker": 150.00,
        "totalWithdrawNumberOfKlasPoker": 1
      },
      "monthlyStatistics": {
        "totalDepositAmount": 5000.00,
        "totalDepositNumber": 15,
        "totalWithdrawAmount": 3500.00,
        "totalWithdrawNumber": 8,
        "maxDepositAmount": 1000.00,
        "maxWithdrawAmount": 800.00,
        "manualTotalDepositAmount": 2000.00,
        "manualTotalDepositNumber": 5,
        "manualTotalWithdrawAmount": 1500.00,
        "totalQrReferenceDepositAmount": 1200.00,
        "totalQrReferenceDepositNumber": 3,
        "manualTotalWithdrawNumber": 4,
        "totalBonusWin": 300.00,
        "totalAssignedBonus": 800.00,
        "totalActivatedBonusAmount": 600.00,
        "totalDiscount": 5,
        "totalDiscountAmount": 150.00,
        "totalRakeback": 8,
        "totalRakebackAmount": 200.00,
        "totalAtmOutageAmount": 50.00,
        "totalAtmOutageNumber": 2,
        "lastTransactionDate": "2024-01-20",
        "firstDepositDate": "2024-01-01",
        "firstWithdrawalRequestDate": "2024-01-05",
        "lastWithdrawalRequestDate": "2024-01-18",
        "lastWithdrawalProcessDate": "2024-01-19",
        "lastDepositDate": "2024-01-20"
      }
    },
    "customerId": "12345"
  },
  "message": "Customer statistics retrieved successfully"
}
```

### Programmatic Usage
```typescript
import { CustomerDetailsPreloadRequest } from '../CustomerDetailsPreloadRequest';
import {
  CustomerDetailsStatisticsTabChangeRequest,
  CustomerDetailsStatisticsWeeklyStatisticsRequest,
  CustomerDetailsStatisticsDailyStatisticsRequest,
  CustomerDetailsStatisticsMonthlyStatisticsRequest
} from './statistics';

// Step 1: Preload to get dynamic element IDs (shared preload request)
const preloadRequest = new CustomerDetailsPreloadRequest({
  customerId: '12345'
});

const preloadResponse = await dagurClient.execute(preloadRequest);
if (!preloadResponse.success) {
  throw new Error('Preload failed');
}

// Step 2: Tab change to get statistics data
const tabChangeRequest = new CustomerDetailsStatisticsTabChangeRequest({
  customerId: '12345',
  javax: preloadResponse.data,
  viewState: preloadResponse.viewState!
});

const statisticsResponse = await dagurClient.execute(tabChangeRequest);
if (statisticsResponse.success) {
  console.log('Customer statistics:', statisticsResponse.data.statistics);
}

// Step 3: Load weekly statistics data (optional)
const weeklyStatsRequest = new CustomerDetailsStatisticsWeeklyStatisticsRequest({
  customerId: '12345',
  javax: preloadResponse.data,
  viewState: statisticsResponse.viewState! // Use viewState from previous request
});

const weeklyStatsResponse = await dagurClient.execute(weeklyStatsRequest);
if (weeklyStatsResponse.success) {
  console.log('Customer weekly statistics:', weeklyStatsResponse.data.statistics.weeklyStatistics);
}

// Step 4: Load daily statistics data (optional)
const dailyStatsRequest = new CustomerDetailsStatisticsDailyStatisticsRequest({
  customerId: '12345',
  javax: preloadResponse.data,
  viewState: weeklyStatsResponse.viewState! // Use viewState from previous request
});

const dailyStatsResponse = await dagurClient.execute(dailyStatsRequest);
if (dailyStatsResponse.success) {
  console.log('Customer daily statistics:', dailyStatsResponse.data.statistics.dailyStatistics);
}

// Step 5: Load monthly statistics data (optional)
const monthlyStatsRequest = new CustomerDetailsStatisticsMonthlyStatisticsRequest({
  customerId: '12345',
  javax: preloadResponse.data,
  viewState: dailyStatsResponse.viewState! // Use viewState from previous request
});

const monthlyStatsResponse = await dagurClient.execute(monthlyStatsRequest);
if (monthlyStatsResponse.success) {
  console.log('Customer monthly statistics:', monthlyStatsResponse.data.statistics.monthlyStatistics);
}
```

## Form Data Structure

The tab change request uses the following form data structure:

```
javax.faces.partial.ajax=true
javax.faces.source=form:tabView
javax.faces.partial.execute=form:tabView
javax.faces.partial.render=form:tabView
javax.faces.behavior.event=tabChange
javax.faces.partial.event=tabChange
form:tabView_contentLoad=true
form:tabView_newTab=form:tabView:statistics
form:tabView_tabindex=1
form=form
form:tabView:j_idt627_input=on
form:tabView:j_idt629:0:j_idt630_collapsed=false
form:tabView:customerNotesDt_rppDD=10
form:tabView_activeIndex=1
javax.faces.ViewState={viewState}
```

## Dynamic JSF Elements

- **j_idt627**: Checkbox/toggle input element (from shared preload request)
- **j_idt629**: Collapsible panel element (from shared preload request)
- **j_idt630**: Nested collapsed state element

## Statistics Data Structure

The response contains a `statistics` object with the following fields:

### Currently Implemented

#### Game Statistics (`gameStatistics`)
All fields are parsed from the HTML table structure with the same pattern:

**String Fields:**
- `lastBetDate`: Last bet date
- `incomeAffiliateCode`: Income affiliate code
- `trackboxAffiliateId`: Trackbox affiliate ID
- `klasPokerUsername`: Klas Poker username

**Numeric Fields:**
- `totalBonusWin`: Total bonus win amount
- `totalAssignedBonus`: Total assigned bonus amount
- `totalActivatedBonusAmount`: Total activated bonus amount
- `totalFreebet`: Total number of freebets
- `totalUsedFreebet`: Total number of used freebets
- `totalUnusedFreebet`: Total number of unused freebets
- `totalJackpotJoinings`: Total jackpot joinings count
- `totalJackpotWinningCount`: Total jackpot winning count
- `totalJackpotWinningAmount`: Total jackpot winning amount
- `totalPayout`: Total payout amount
- `totalDiscount`: Total discount count
- `totalDiscountAmount`: Total discount amount
- `totalRakeback`: Total rakeback count
- `totalRakebackAmount`: Total rakeback amount
- `totalDepositOfKlasPoker`: Total deposit of Klas Poker
- `totalDepositNumberOfKlasPoker`: Total deposit number of Klas Poker
- `totalWithdrawOfKlasPoker`: Total withdraw of Klas Poker
- `totalWithdrawNumberOfKlasPoker`: Total withdraw number of Klas Poker
- `averageStake`: Average stake amount
- `lifetimeTurnover`: Lifetime turnover amount

### Planned Fields (for future implementation)
- `totalDeposits`: Total deposits amount
- `totalWithdrawals`: Total withdrawals amount
- `totalBets`: Total bets amount
- `totalWins`: Total wins amount
- `netGamingRevenue`: Net gaming revenue
- `depositCount`: Number of deposits
- `withdrawalCount`: Number of withdrawals
- `betCount`: Number of bets
- `lastDepositDate`: Last deposit date
- `lastWithdrawalDate`: Last withdrawal date
- `lastBetDate`: Last bet date
- `accountBalance`: Current account balance
- `bonusBalance`: Current bonus balance

## Implementation Notes

- The statistics tab uses `activeIndex=1` (second tab)
- The `parseStatisticsHTML` method needs to be implemented based on the actual HTML structure returned by the server
- Dynamic JSF element IDs are extracted during the preload phase and used in subsequent requests
- ViewState is maintained across requests for proper JSF session handling

## HTML Parsing Implementation

The parsing follows the DRY (Don't Repeat Yourself) principle with a smart parser that can handle multiple statistics with the same table structure.

### Table Structure Patterns
Statistics follow two main HTML patterns:

**Pattern 1: Label Structure (most common)**
```html
<tr>
  <td><label id="form:tabView:j_idt848">Total Freebet</label></td>
  <td><label id="form:tabView:j_idt849">:</label></td>
  <td><label id="form:tabView:j_idt850">9</label></td>
</tr>
```

**Pattern 2: Span with Tooltip Wrapper Structure**
```html
<tr>
  <td><label id="form:tabView:j_idt862">Total Payout</label></td>
  <td><label id="form:tabView:j_idt863">:</label></td>
  <td><span id="form:tabView:j_idt864:tooltipWrapper"><span id="form:tabView:j_idt864:text">0.00</span></span></td>
</tr>
```

The parser automatically detects and handles both structures.

### Smart Parser Configuration
New statistics can be easily added by updating the `statisticsToExtract` array:
```typescript
const statisticsToExtract = [
  { displayName: 'Last Bet Date', propertyName: 'lastBetDate', isString: true },
  { displayName: 'Total Freebet', propertyName: 'totalFreebet' },
  { displayName: 'Total Unused Freebet', propertyName: 'totalUnusedFreebet' },
  // Add more statistics here...
];
```

**Configuration Options:**
- `displayName`: The exact text that appears in the first column of the HTML table
- `propertyName`: The camelCase property name for the JSON response
- `isString`: Optional flag for string values (defaults to numeric parsing)

The `extractStatisticValue` method ignores dynamic j_idt IDs and focuses on the text content pattern, making it robust against JSF ID changes.

### Handling Null Values
Some statistics may have empty values in the HTML:
```html
<tr>
  <td><label>Income Affiliate Code</label></td>
  <td><label>:</label></td>
  <td><label id="form:tabView:j_idt819" class="ui-outputlabel ui-widget"></label></td>
</tr>
```

When the third column's label is empty, the parser returns `null` and the field is included in the response with a `null` value.

### Value Processing Behavior

- **Empty values**: Returns `null` (included in response as `null`)
- **String fields**: Returns raw text value
- **Numeric fields**: Uses `parseFloat()` to handle both integers and decimals
- **Parse failures**: Returns string value as fallback with warning
- **Missing fields**: Returns `null` if statistic not found in HTML
- **All fields**: Always included in response, even when `null`

## TODO

- [x] Implement HTML parsing for Total Freebet count
- [x] Implement DRY parsing approach for multiple statistics
- [x] Add parsing for Total Unused Freebet count
- [x] Add support for span/tooltip wrapper HTML structure
- [x] Add support for decimal/float values in addition to integers
- [x] Implement all 24 requested statistics fields
- [ ] Add error handling for malformed HTML structures
- [ ] Add validation for statistics data types and formats
