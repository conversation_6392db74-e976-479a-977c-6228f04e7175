import fetch from 'node-fetch';
import { TOTP } from 'totp-generator';
import { LoginRequest } from './requests/auth/LoginRequest';
import { IPGCasinoTraderHttpClient } from './pg-ct';
import { PGCasinoTraderRequest } from './requests/Request';
import { findJavaxElementIdBefore, findJavaxViewState } from '../pg-dagur/utils/javax';
import { LoginWithOtpRequest } from './requests/auth/LoginWithOtpRequest';

const BASE_URL = 'https://ct.pgbo.io';

export interface PronetCasinoTraderSession {
  cookies: {
    'primefaces.download': string;
    'language-id': string;
    JSESSIONID: string;
    __nxqsec: string;
    __nxquid: string;
  };
}

/**
 * Pronet API Client
 *
 * This client handles all communication with the Pronet API system.
 * It automatically handles authentication, checksum generation, and request formatting.
 */
export class PGCasinoTraderHttpClient implements IPGCasinoTraderHttpClient {
  // accounting:

  async makeRequest<ResponseModel extends any, Request extends PGCasinoTraderRequest<ResponseModel>>(
    request: Request,
    viewState: string = '',
    additionalHeaders: Record<string, any> = {},
  ): Promise<ReturnType<Request['validateResponse']>> {
    try {
      const url = `${BASE_URL}${request.getPath()}`;

      console.log('🚀 Making request...');
      console.log(`📍 URL: ${request.getMethod()} ${url}`);

      let body: URLSearchParams | undefined = undefined;
      if (request.getMethod() !== 'GET') {
        body = new URLSearchParams({
          'javax.faces.ViewState': viewState,
          // ...request.getBody(),
        });

        const reqBody = request.getBody();
        if (reqBody instanceof URLSearchParams) {
          for (const [k, v] of reqBody.entries()) {
            body.append(k, v);
          }
        } else if (typeof reqBody === 'object') {
          for (const [k, v] of Object.entries(reqBody)) {
            if (typeof v !== 'undefined') {
              body.append(k, v?.toString() || '');
            }
          }
        }
      }

      const response = await fetch(url, {
        method: request.getMethod(),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...request.getHeaders(),
          ...additionalHeaders,
        },
        body: body,
      });
      console.log(body);
      console.log(body?.toString());
      console.log({
        'Content-Type': 'application/x-www-form-urlencoded',
        ...request.getHeaders(),
        ...additionalHeaders,
      });

      const data = await response.text();

      if (data.includes('redirect url="/login.xhtml"')) {
        throw new Error('Unauthorized');
      }

      if (!response.ok) {
        console.error('❌ Response Error Body:', data);
        throw new Error(`Request failed: ${response.status} ${response.statusText} - ${data}`);
      }

      console.log('📥 Response Body:', data.slice(0, 512));

      return request.validateResponse(data) as ReturnType<Request['validateResponse']>;
    } catch (error) {
      console.error('❌ Request failed:', error);
      throw error;
    }
  }

  async getInitialData(): Promise<{
    cookies: PronetCasinoTraderSession['cookies'];
    viewState: string;
    loginButtonId: string;
  }> {
    // English
    const languageId = '2';
    let nxquid = null;
    let nxqsec = null;

    let response = await fetch(`${BASE_URL}/casino-trader/login.xhtml`, {
      redirect: 'manual',
    });
    if (response.status === 302) {
      let setCookieHeaders = response.headers.raw()['set-cookie'];
      if (!setCookieHeaders || setCookieHeaders.length === 0) {
        throw new Error('No set-cookie headers found');
      }

      nxquid = setCookieHeaders.find((header) => header.includes('__nxquid'));
      if (!nxquid) {
        throw new Error('__nxquid cookie not found');
      }
      nxquid = nxquid.split(';')[0]?.split('=').slice(1).join('=') || '';

      response = await fetch(`${BASE_URL}/casino-trader/login.xhtml`, {
        headers: {
          Cookie: `__nxquid=${nxquid}`,
        },
      });

      setCookieHeaders = response.headers.raw()['set-cookie'];
      if (!setCookieHeaders || setCookieHeaders.length === 0) {
        throw new Error('No set-cookie headers found');
      }

      nxqsec = setCookieHeaders.find((header) => header.includes('__nxqsec'));
      if (!nxqsec) {
        throw new Error('__nxqsec cookie not found');
      }
      nxqsec = nxqsec.split(';')[0]?.split('=').slice(1).join('=') || '';
    } else if (response.ok) {
      // do nothing
    } else {
      throw new Error(`Failed to prelogin: expected redirect to set __nxquid.`);
    }

    let data = await response.text();
    if (data.includes('nexusguard')) {
      const startIndex = data.indexOf('/casino-trader/login.xhtml?vcode=');
      if (startIndex === -1) {
        throw new Error('Failed to find login url');
      }

      const endIndex = data.indexOf('"', startIndex);
      if (endIndex === -1) {
        throw new Error('Failed to find end of login url');
      }

      const code = data.substring(startIndex + '/casino-trader/login.xhtml?vcode='.length, endIndex);
      response = await fetch(`${BASE_URL}/casino-trader/login.xhtml?vcode=${code}`, {
        headers: {
          Cookie: `__nxquid=${nxquid}; __nxqsec=${nxqsec}`,
        },
        redirect: 'manual',
      });
      if (response.status === 302) {
        const setCookieHeaders = response.headers.raw()['set-cookie'];
        if (!setCookieHeaders || setCookieHeaders.length === 0) {
          throw new Error('No set-cookie headers found');
        }

        nxqsec = setCookieHeaders.find((header) => header.includes('__nxqsec'));
        if (!nxqsec) {
          throw new Error('__nxqsec cookie not found');
        }
        nxqsec = nxqsec.split(';')[0]?.split('=').slice(1).join('=') || '';

        response = await fetch(`${BASE_URL}/casino-trader/login.xhtml`, {
          headers: {
            Cookie: `__nxquid=${nxquid}; __nxqsec=${nxqsec}`,
          },
        });
      } else {
        throw new Error(`Failed to prelogin: expected redirect to set __nxqsec.`);
      }

      data = await response.text();
    }

    const setCookieHeaders = response.headers.raw()['set-cookie'];
    if (!setCookieHeaders || setCookieHeaders.length === 0) {
      throw new Error('No set-cookie headers found');
    }

    let jsessionid = setCookieHeaders.find((header) => header.includes('JSESSIONID'));
    if (!jsessionid) {
      throw new Error('JSESSIONID cookie not found');
    }
    jsessionid = jsessionid.split(';')[0]?.split('=')[1] || '';

    const viewState = findJavaxViewState(data);
    if (!viewState) {
      throw new Error('Failed to find view state');
    }

    const loginButtonId = findJavaxElementIdBefore(data, '', '<span class="ui-button-text ui-c">Giriş</span>');
    if (!loginButtonId) {
      throw new Error('Failed to find login button id');
    }

    return {
      cookies: {
        'primefaces.download': 'true',
        'language-id': languageId,
        JSESSIONID: jsessionid,
        __nxqsec: nxqsec || '',
        __nxquid: nxquid || '',
      },
      loginButtonId,
      viewState: viewState,
    };
  }

  async loginWithOtp(
    username: string,
    password: string,
    traderCode: string,
    otp: string,
  ): Promise<PronetCasinoTraderSession> {
    const { cookies, viewState, loginButtonId } = await this.getInitialData();

    const Cookie = Object.keys(cookies)
      .map((key) => `${key}=${cookies[key as keyof typeof cookies]}`)
      .join('; ');

    const response = await this.makeRequest(
      new LoginRequest({ username, password, traderCode, loginButtonId }),
      viewState,
      {
        Cookie: Cookie,
      },
    );

    if (response.success) {
      const response2 = await this.makeRequest(
        new LoginWithOtpRequest({
          otp,
          otpInputId: response.data.otpInputId,
          loginButtonId: response.data.loginButtonId,
        }),
        response.viewState,
        {
          Cookie: Cookie,
        },
      );

      if (response2.success) {
        return {
          cookies: cookies,
        };
      } else {
        throw new Error(`Failed to login: ${response2.message}`);
      }
    } else {
      throw new Error(`Failed to login: ${response.message}`);
    }
  }
}

export class PGCasinoTraderAdminHttpClient implements IPGCasinoTraderHttpClient {
  private client: PGCasinoTraderHttpClient;
  private session: PronetCasinoTraderSession | null = null;
  private isLoggingIn = false;

  constructor(client: PGCasinoTraderHttpClient) {
    this.client = client;
  }

  async makeRequest<ResponseModel extends any, Request extends PGCasinoTraderRequest<ResponseModel>>(
    request: Request,
    viewState: string = '',
    additionalHeaders: Record<string, any> = {},
  ): Promise<ReturnType<Request['validateResponse']>> {
    if (!this.session) {
      await this.loginWithOtpSecret(
        process.env['PG_CT_USERNAME'] || '',
        process.env['PG_CT_PASSWORD'] || '',
        process.env['PG_CT_TRADERCODE'] || '',
        process.env['PG_CT_OTP_SECRET'] || '',
      );
    }

    const session = this.session;
    if (!session) {
      throw new Error('Failed to login');
    }

    const Cookie = Object.keys(session.cookies)
      .map((key) => `${key}=${session.cookies[key as keyof PronetCasinoTraderSession['cookies']]}`)
      .join('; ');

    return this.client.makeRequest(request, viewState, {
      ...additionalHeaders,
      Cookie: Cookie,
    });
  }

  private generateTOTP(secret: string): string {
    try {
      console.log(`🔐 Generating TOTP from secret: ${secret.substring(0, 4)}...`);

      const otpCode = TOTP.generate(secret).otp;

      console.log(`🔐 OTP Generated: ${otpCode}`);
      return otpCode;
    } catch (error) {
      console.error(`❌ TOTP generation failed:`, error);
      throw error;
    }
  }

  async loginWithOtpSecret(username: string, password: string, traderCode: string, otpSecret: string) {
    if (this.isLoggingIn) {
      for (let i = 0; i < 60; i++) {
        await new Promise((res) => setTimeout(res, 1000));

        if (this.session) {
          return;
        }

        if (!this.isLoggingIn) {
          break;
        }
      }
    }

    this.isLoggingIn = true;
    const otp = this.generateTOTP(otpSecret);
    this.session = await this.client.loginWithOtp(username, password, traderCode, otp);
    this.isLoggingIn = false;
  }
}

// Export a singleton instance
export const pgCasinoTraderHttpClient = new PGCasinoTraderHttpClient();
export const pgCasinoTraderAdminHttpClient = new PGCasinoTraderAdminHttpClient(pgCasinoTraderHttpClient);
