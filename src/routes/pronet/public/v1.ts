import { Router } from 'express';
import { AuthController } from '@/controllers/pronet/auth.controller';
import { PronetBonusController } from '@/controllers/pronet/bonus.controller';
import { SiteClaimableBonusController } from '@/controllers/pronet/siteClaimableBonus.controller';
import { PronetLossbackBonusController } from '@/controllers/pronet/lossbackBonus.controller';
import { PronetCustomersController } from '@/controllers/pronet/customers.controller';

const router = Router();

router.post('/auth/dagur/login', AuthController.loginWithDagur);

// Site claimable bonus endpoints
router.get('/site-claimable-bonuses', SiteClaimableBonusController.list);
router.put('/site-claimable-bonuses/:slotName', SiteClaimableBonusController.updateSlot);
router.get('/site-claimable-bonuses/check/:bonusId', SiteClaimableBonusController.checkClaimable);
router.get('/site-claimable-bonuses/claimable', SiteClaimableBonusController.getClaimableBonuses);
router.patch('/site-claimable-bonuses/:slotName/toggle', SiteClaimableBonusController.toggleSlotActive);
// Customer endpoints
router.get('/customers', PronetCustomersController.list);
router.get('/customers/total-balances', PronetCustomersController.getTotalBalances);
router.get('/customers/:id', PronetCustomersController.getById);

router.get('/freespin-bonuses', PronetBonusController.listFreespinBonuses);
router.get('/freespin-bonuses/providers', PronetBonusController.listFreespinBonusProviders);
router.get('/freespin-bonuses/providers/:id/games', PronetBonusController.listFreespinBonusGames);
router.get('/freespin-bonuses/providers/:id/bet-amounts', PronetBonusController.listFreespinBetAmounts);
router.get('/freespin-bonuses/providers/:id/currencies', PronetBonusController.listFreespinCurrencies);
router.get('/freespin-bonuses/:id', PronetBonusController.getFreespinBonus);
router.post('/freespin-bonuses/:id/bulk-assignment-jobs', PronetBonusController.createFreespinBonusBulkAssignmentJob);
router.post('/freespin-bonuses', PronetBonusController.createFreespinBonus);

router.get('/freespin-bonus-templates', PronetBonusController.listFreespinBonusTemplates);
router.get('/freespin-bonus-templates/:id', PronetBonusController.getFreespinBonusTemplate);
router.post('/freespin-bonus-templates', PronetBonusController.createFreespinBonusTemplate);

router.get('/bonuses', PronetBonusController.searchBonuses);
router.patch('/bonuses/:id', PronetBonusController.patchBonus);
router.delete('/bonuses/:id', PronetBonusController.softDeleteBonus);
router.post('/bonuses/:id/claims', PronetBonusController.claimBonus);
router.post('/bonuses/:id/promocodes', PronetBonusController.createBonusPromocode);

router.get('/bonus-claims', PronetBonusController.searchBonusClaims);

router.get('/bonus-promocodes', PronetBonusController.searchBonusPromocodes);
router.patch('/bonus-promocodes/:id', PronetBonusController.toggleBonusPromocode);
router.delete('/bonus-promocodes/:id', PronetBonusController.softDeleteBonusPromocode);
router.post('/bonus-promocodes/:code/activations', PronetBonusController.activateBonusPromocode);

router.get('/bonus-promocode-activations', PronetBonusController.searchBonusPromocodeActivations);

router.delete('/bonus-templates/:id', PronetBonusController.softDeleteBonusTemplate);

// Lossback bonus routes
router.get('/lossback-bonuses', PronetLossbackBonusController.listLossbackBonuses);
router.get('/lossback-bonuses/:id', PronetLossbackBonusController.getLossbackBonus);
router.post('/lossback-bonuses', PronetLossbackBonusController.createLossbackBonus);

router.get('/lossback-bonus-templates', PronetLossbackBonusController.listLossbackBonusTemplates);
router.get('/lossback-bonus-templates/:id', PronetLossbackBonusController.getLossbackBonusTemplate);
router.post('/lossback-bonus-templates', PronetLossbackBonusController.createLossbackBonusTemplate);

router.get('/bonus-bulk-assignment-jobs', PronetBonusController.listBonusBulkAssignmentJobs);
router.get('/bonus-bulk-assignment-jobs/:id', PronetBonusController.getBonusBulkAssignmentJob);
router.post('/bonus-bulk-assignment-jobs/:id/cancellations', PronetBonusController.cancelBonusBulkAssignmentJob);
router.post('/bonus-bulk-assignment-jobs/:id/retries', PronetBonusController.retryBonusBulkAssignmentJob);

export default router;
