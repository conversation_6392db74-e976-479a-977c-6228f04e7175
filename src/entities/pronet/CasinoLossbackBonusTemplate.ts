import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BonusTemplate } from './BonusTemplate';

@Entity('csn_lossback_bonus_templates', { schema: 'pronet' })
export class CasinoLossbackBonusTemplate {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'integer' })
  bonusTemplateId!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  maxBalance!: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  lossbackPercentage!: number;

  @Column({ type: 'time', nullable: true })
  happyHoursStart!: string | null;

  @Column({ type: 'time', nullable: true })
  happyHoursEnd!: string | null;

  @Column({ type: 'integer' })
  validForDays!: number;

  @ManyToOne('BonusTemplate')
  @JoinColumn({ name: 'bonusTemplateId', referencedColumnName: 'id' })
  bonusTemplate!: BonusTemplate;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}