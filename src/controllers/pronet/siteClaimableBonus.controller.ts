import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/utils/asyncHandler';
import { ApiResponse } from '@/types/api';
import { ValidationError } from '@/types/errors';
import { SiteClaimableBonusService, SiteClaimableBonusUpdateInput } from '@/services/pronet/siteClaimableBonus.service';

export interface SiteClaimableBonusUpdateRequest {
  bonusId: number;
  isActive?: boolean;
}

export class SiteClaimableBonusController {
  /**
   * GET /api/pronet/v1/site-claimable-bonuses - List all claimable bonus configurations
   */
  static list = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📋 Listing site claimable bonus configurations');

      const service = new SiteClaimableBonusService();
      const configurations = await service.list();

      console.log(`✅ Retrieved ${configurations.length} site claimable bonus configurations`);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Site claimable bonus configurations retrieved successfully',
        data: configurations,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to list site claimable bonus configurations:', error);

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to retrieve configurations: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * PUT /api/pronet/v1/site-claimable-bonuses/:slotName - Update slot configuration
   */
  static updateSlot = asyncHandler(async (req: Request, res: Response) => {
    try {
      const slotName = req.params['slotName'];
      const updateData: SiteClaimableBonusUpdateRequest = req.body;

      console.log(`🔄 Updating site claimable bonus slot: ${slotName}`, updateData);

      // Validate slot name
      if (!slotName || typeof slotName !== 'string') {
        throw new ValidationError('Slot name is required and must be a string');
      }

      // Validate request body
      if (!updateData.bonusId || typeof updateData.bonusId !== 'number') {
        throw new ValidationError('bonusId is required and must be a number');
      }

      if (updateData.bonusId < 1) {
        throw new ValidationError('bonusId must be a positive integer');
      }

      if (updateData.isActive !== undefined && typeof updateData.isActive !== 'boolean') {
        throw new ValidationError('isActive must be a boolean');
      }

      const service = new SiteClaimableBonusService();
      const updatedConfiguration = await service.update(slotName, {
        bonusId: updateData.bonusId,
        isActive: updateData.isActive,
      });

      console.log(`✅ Updated site claimable bonus slot: ${slotName}`);

      const response: ApiResponse<any> = {
        success: true,
        message: `Slot '${slotName}' updated successfully`,
        data: updatedConfiguration,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to update site claimable bonus slot:', error);

      if (error instanceof ValidationError) {
        throw error;
      }

      if (error instanceof Error) {
        // Check for specific error types
        if (error.message.includes('not found')) {
          return res.status(404).json({
            success: false,
            message: error.message,
            timestamp: new Date().toISOString(),
          });
        }

        if (error.message.includes('not active') || 
            error.message.includes('expired') || 
            error.message.includes('deleted')) {
          throw new ValidationError(error.message);
        }

        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to update slot: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /api/pronet/v1/site-claimable-bonuses/check/:bonusId - Verify if bonus is claimable
   */
  static checkClaimable = asyncHandler(async (req: Request, res: Response) => {
    try {
      const bonusIdParam = req.params['bonusId'];
      const bonusId = parseInt(bonusIdParam);

      console.log(`🔍 Checking if bonus is claimable: ${bonusId}`);

      // Validate bonus ID
      if (isNaN(bonusId) || bonusId < 1) {
        throw new ValidationError('Bonus ID must be a positive integer');
      }

      const service = new SiteClaimableBonusService();
      const isClaimable = await service.isClaimable(bonusId);

      console.log(`✅ Bonus ${bonusId} claimable status: ${isClaimable}`);

      const response: ApiResponse<any> = {
        success: true,
        message: `Bonus claimable status checked successfully`,
        data: {
          bonusId,
          isClaimable,
        },
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to check bonus claimable status:', error);

      if (error instanceof ValidationError) {
        throw error;
      }

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to check bonus status: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * GET /api/pronet/v1/site-claimable-bonuses/claimable - Get all currently claimable bonuses
   */
  static getClaimableBonuses = asyncHandler(async (req: Request, res: Response) => {
    try {
      console.log('📋 Getting all currently claimable bonuses');

      const service = new SiteClaimableBonusService();
      const claimableBonuses = await service.getClaimableBonuses();

      console.log(`✅ Retrieved ${claimableBonuses.length} claimable bonuses`);

      const response: ApiResponse<any> = {
        success: true,
        message: 'Claimable bonuses retrieved successfully',
        data: claimableBonuses,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to get claimable bonuses:', error);

      if (error instanceof Error) {
        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to retrieve claimable bonuses: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });

  /**
   * PATCH /api/pronet/v1/site-claimable-bonuses/:slotName/toggle - Toggle slot active status
   */
  static toggleSlotActive = asyncHandler(async (req: Request, res: Response) => {
    try {
      const slotName = req.params['slotName'];
      const { isActive } = req.body;

      console.log(`🔄 Toggling slot active status: ${slotName} -> ${isActive}`);

      // Validate slot name
      if (!slotName || typeof slotName !== 'string') {
        throw new ValidationError('Slot name is required and must be a string');
      }

      // Validate isActive
      if (typeof isActive !== 'boolean') {
        throw new ValidationError('isActive is required and must be a boolean');
      }

      const service = new SiteClaimableBonusService();
      const updatedConfiguration = await service.toggleActive(slotName, isActive);

      console.log(`✅ Toggled slot active status: ${slotName}`);

      const response: ApiResponse<any> = {
        success: true,
        message: `Slot '${slotName}' active status updated successfully`,
        data: updatedConfiguration,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('❌ Failed to toggle slot active status:', error);

      if (error instanceof ValidationError) {
        throw error;
      }

      if (error instanceof Error) {
        // Check for specific error types
        if (error.message.includes('not found')) {
          return res.status(404).json({
            success: false,
            message: error.message,
            timestamp: new Date().toISOString(),
          });
        }

        // Check if it's a database error
        if (error.message.includes('database') || error.message.includes('connection')) {
          throw new ValidationError(`Failed to toggle slot status: ${error.message}`);
        }
      }

      // Re-throw the error to be handled by the global error handler
      throw error;
    }
  });
}
