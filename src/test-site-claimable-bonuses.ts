/**
 * Test script for the Site Claimable Bonuses implementation
 * 
 * This script tests the new site claimable bonuses endpoints to ensure they work correctly.
 * Run this after starting the server and running the migration to verify the implementation.
 */

import fetch from 'node-fetch';

const BASE_URL = process.env['BASE_URL'] || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/pronet/v1`;

async function testSiteClaimableBonusesEndpoints() {
  console.log('🧪 Testing Site Claimable Bonuses endpoints...\n');

  try {
    // Test 1: List all site claimable bonus configurations
    console.log('📋 Test 1: List all site claimable bonus configurations');
    const listResponse = await fetch(`${API_BASE}/site-claimable-bonuses`);
    const listData = await listResponse.json();
    
    console.log(`Status: ${listResponse.status}`);
    console.log(`Response:`, JSON.stringify(listData, null, 2));
    
    if (listResponse.ok && listData.success) {
      console.log('✅ List configurations test passed\n');
    } else {
      console.log('❌ List configurations test failed\n');
    }

    // Test 2: Get claimable bonuses
    console.log('📋 Test 2: Get currently claimable bonuses');
    const claimableResponse = await fetch(`${API_BASE}/site-claimable-bonuses/claimable`);
    const claimableData = await claimableResponse.json();
    
    console.log(`Status: ${claimableResponse.status}`);
    console.log(`Response:`, JSON.stringify(claimableData, null, 2));
    
    if (claimableResponse.ok && claimableData.success) {
      console.log('✅ Get claimable bonuses test passed\n');
    } else {
      console.log('❌ Get claimable bonuses test failed\n');
    }

    // Test 3: Check if a specific bonus is claimable
    console.log('🔍 Test 3: Check if bonus ID 1 is claimable');
    const checkResponse = await fetch(`${API_BASE}/site-claimable-bonuses/check/1`);
    const checkData = await checkResponse.json();
    
    console.log(`Status: ${checkResponse.status}`);
    console.log(`Response:`, JSON.stringify(checkData, null, 2));
    
    if (checkResponse.ok && checkData.success) {
      console.log('✅ Check claimable test passed\n');
    } else {
      console.log('❌ Check claimable test failed\n');
    }

    // Test 4: Try to update a slot (this will likely fail without a valid bonus ID)
    console.log('🔄 Test 4: Try to update slot_1 configuration');
    const updateResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_1`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bonusId: 1,
        isActive: true,
      }),
    });
    const updateData = await updateResponse.json();
    
    console.log(`Status: ${updateResponse.status}`);
    console.log(`Response:`, JSON.stringify(updateData, null, 2));
    
    if (updateResponse.ok && updateData.success) {
      console.log('✅ Update slot test passed\n');
    } else {
      console.log('❌ Update slot test failed (expected if no valid bonus exists)\n');
    }

    // Test 5: Try to toggle slot active status
    console.log('🔄 Test 5: Try to toggle slot_1 active status');
    const toggleResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_1/toggle`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isActive: false,
      }),
    });
    const toggleData = await toggleResponse.json();
    
    console.log(`Status: ${toggleResponse.status}`);
    console.log(`Response:`, JSON.stringify(toggleData, null, 2));
    
    if (toggleResponse.ok && toggleData.success) {
      console.log('✅ Toggle slot test passed\n');
    } else {
      console.log('❌ Toggle slot test failed\n');
    }

    // Test 6: Invalid bonus ID check
    console.log('🔍 Test 6: Check invalid bonus ID');
    const invalidCheckResponse = await fetch(`${API_BASE}/site-claimable-bonuses/check/invalid`);
    const invalidCheckData = await invalidCheckResponse.json();
    
    console.log(`Status: ${invalidCheckResponse.status}`);
    console.log(`Response:`, JSON.stringify(invalidCheckData, null, 2));
    
    if (invalidCheckResponse.status === 400 && !invalidCheckData.success) {
      console.log('✅ Invalid bonus ID test passed\n');
    } else {
      console.log('❌ Invalid bonus ID test failed\n');
    }

    // Test 7: Non-existent slot update
    console.log('🔄 Test 7: Try to update non-existent slot');
    const nonExistentResponse = await fetch(`${API_BASE}/site-claimable-bonuses/slot_999`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bonusId: 1,
        isActive: true,
      }),
    });
    const nonExistentData = await nonExistentResponse.json();
    
    console.log(`Status: ${nonExistentResponse.status}`);
    console.log(`Response:`, JSON.stringify(nonExistentData, null, 2));
    
    if (nonExistentResponse.status === 404 && !nonExistentData.success) {
      console.log('✅ Non-existent slot test passed\n');
    } else {
      console.log('❌ Non-existent slot test failed\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  testSiteClaimableBonusesEndpoints();
}

export { testSiteClaimableBonusesEndpoints };
